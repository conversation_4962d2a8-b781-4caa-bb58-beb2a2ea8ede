server {
    listen ${NGINX_PORT};
    server_name ${NGINX_SERVER_NAME};
    client_max_body_size ${NGINX_CLIENT_MAX_BODY_SIZE};

    # 为了更好的用户体验，将根路径的访问重定向到 /dify/apps
    location = / {
        return 301 /dify/apps;
    }

    # ================== API 代理规则 (剥离 /dify 前缀) ==================
    # 使用正则表达式匹配所有需要代理到 api:5001 的路径
    location ~ ^/dify/(console/api|api|v1|files|mcp|datasets)/ {
        # 移除 /dify/ 前缀
        rewrite ^/dify/(.*)$ /$1 break;

        # 代理到 API 服务
        proxy_pass http://api:5001;

        # 内联代理配置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout ${NGINX_PROXY_SEND_TIMEOUT};
        proxy_read_timeout ${NGINX_PROXY_READ_TIMEOUT};
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # 特殊处理 token-login 的 Header
    location ~ ^/dify/(console/api/token-login|console/api/refresh-token|api/passport) {
        proxy_set_header Authorization "Bearer $http_access_token";
        rewrite ^/dify/(.*)$ /$1 break;
        proxy_pass http://api:5001;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout ${NGINX_PROXY_SEND_TIMEOUT};
        proxy_read_timeout ${NGINX_PROXY_READ_TIMEOUT};
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # 插件代理规则 (剥离 /dify 前缀)
    location /dify/e/ {
        rewrite ^/dify/(.*)$ /$1 break;
        proxy_pass http://plugin_daemon:5002;
        proxy_set_header Dify-Hook-Url $scheme://$host$request_uri;

        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout ${NGINX_PROXY_SEND_TIMEOUT};
        proxy_read_timeout ${NGINX_PROXY_READ_TIMEOUT};
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # ================== Web 前端代理规则 (保留 /dify 前缀) ==================
    # 捕获所有剩下的 /dify/ 请求 (页面、静态资源等)
    location /dify/ {
        # 直接代理到 Web 服务，不修改 URI
        proxy_pass http://web:3000;

        # 内联代理配置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout ${NGINX_PROXY_SEND_TIMEOUT};
        proxy_read_timeout ${NGINX_PROXY_READ_TIMEOUT};
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # placeholder for acme challenge location
    ${ACME_CHALLENGE_LOCATION}
    # placeholder for https config defined in https.conf.template
    ${HTTPS_CONFIG}
}
